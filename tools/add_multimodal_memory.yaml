identity:
  name: add_multimodal_memory
  author: moch<PERSON> (v0.3.0 Optimization by mocha, Original by <PERSON><PERSON><PERSON>)
  label:
    en_US: 🎨 Add Multimodal Memory
    zh_Hans: 🎨 添加多模态记忆
    pt_BR: 🎨 Adicionar Memória Multimodal
    ja_JP: 🎨 マルチモーダルメモリ追加
description:
  human:
    en_US: Add multimodal memories (text, images, documents, PDFs) to Mem0. Supports all content types including text, image_url, mdx_url, and pdf_url. This is the unified tool for all multimodal content processing.
    zh_Hans: 向Mem0添加多模态记忆（文本、图像、文档、PDF）。支持所有内容类型，包括文本、image_url、mdx_url和pdf_url。这是处理所有多模态内容的统一工具。
    pt_BR: Adicionar memórias multimodais (texto, imagens, documentos, PDFs) ao Mem0. Suporta todos os tipos de conteúdo, incluindo texto, image_url, mdx_url e pdf_url. Esta é a ferramenta unificada para processamento de conteúdo multimodal.
    ja_JP: Mem0にマルチモーダルメモリ（テキスト、画像、文書、PDF）を追加します。テキスト、image_url、mdx_url、pdf_urlを含むすべてのコンテンツタイプをサポート。これはすべてのマルチモーダルコンテンツ処理のための統一ツールです。
  llm: This is the unified tool for adding all types of multimodal memories to Mem0. It supports text, images (image_url), documents (mdx_url), and PDFs (pdf_url). Use OpenAI chat message format with proper content type specifications for different media types.

parameters:
  - name: content_type
    type: string
    required: true
    label:
      en_US: Content Type
      zh_Hans: 内容类型
      pt_BR: Tipo de Conteúdo
      ja_JP: コンテンツタイプ
    human_description:
      en_US: Type of content to add - text, image, document, or pdf
      zh_Hans: 要添加的内容类型 - 文本、图像、文档或PDF
      pt_BR: Tipo de conteúdo a adicionar - texto, imagem, documento ou PDF
      ja_JP: 追加するコンテンツの種類 - テキスト、画像、文書、またはPDF
    llm_description: The type of multimodal content to process (text, image, document, pdf)
    form: form
    options:
      - value: text
        label:
          en_US: Text Content
          zh_Hans: 文本内容
          pt_BR: Conteúdo de Texto
          ja_JP: テキストコンテンツ
      - value: image
        label:
          en_US: Image URL
          zh_Hans: 图像URL
          pt_BR: URL da Imagem
          ja_JP: 画像URL
      - value: document
        label:
          en_US: Document URL (TXT/MDX)
          zh_Hans: 文档URL (TXT/MDX)
          pt_BR: URL do Documento (TXT/MDX)
          ja_JP: 文書URL (TXT/MDX)
      - value: pdf
        label:
          en_US: PDF URL
          zh_Hans: PDF URL
          pt_BR: URL do PDF
          ja_JP: PDF URL

  - name: content_text
    type: string
    required: false
    label:
      en_US: Text Content
      zh_Hans: 文本内容
      pt_BR: Conteúdo de Texto
      ja_JP: テキストコンテンツ
    human_description:
      en_US: The text content to add as memory (required when content_type is 'text')
      zh_Hans: 要添加为记忆的文本内容（当content_type为'text'时必需）
      pt_BR: O conteúdo de texto a ser adicionado como memória (obrigatório quando content_type é 'text')
      ja_JP: メモリとして追加するテキストコンテンツ（content_typeが'text'の場合必須）
    llm_description: Text content to process and store as memory
    form: llm

  - name: content_url
    type: string
    required: false
    label:
      en_US: Content URL
      zh_Hans: 内容URL
      pt_BR: URL do Conteúdo
      ja_JP: コンテンツURL
    human_description:
      en_US: URL of the image, document, or PDF file to process (required for image/document/pdf types)
      zh_Hans: 要处理的图像、文档或PDF文件的URL（图像/文档/PDF类型时必需）
      pt_BR: URL da imagem, documento ou arquivo PDF para processar (obrigatório para tipos image/document/pdf)
      ja_JP: 処理する画像、文書、またはPDFファイルのURL（image/document/pdfタイプの場合必須）
    llm_description: URL of the multimodal content to process
    form: llm

  - name: context_description
    type: string
    required: false
    label:
      en_US: Context Description
      zh_Hans: 上下文描述
      pt_BR: Descrição do Contexto
      ja_JP: コンテキスト説明
    human_description:
      en_US: Optional description providing context about the content (helps with better memory extraction)
      zh_Hans: 可选的上下文描述，提供内容相关信息（有助于更好的记忆提取）
      pt_BR: Descrição opcional fornecendo contexto sobre o conteúdo (ajuda com melhor extração de memória)
      ja_JP: コンテンツに関するコンテキストを提供するオプションの説明（より良いメモリ抽出に役立ちます）
    llm_description: Optional context description to improve memory extraction quality
    form: llm

  - name: user_id
    type: string
    required: false
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
      ja_JP: ユーザーID
    human_description:
      en_US: Unique identifier for the user
      zh_Hans: 用户的唯一标识符
      pt_BR: Identificador único do usuário
      ja_JP: ユーザーの一意識別子
    llm_description: The unique identifier for the user whose memories are being added.
    form: llm

  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
      ja_JP: エージェントID
    human_description:
      en_US: Unique identifier for the AI agent
      zh_Hans: AI代理的唯一标识符
      pt_BR: Identificador único do agente de IA
      ja_JP: AIエージェントの一意識別子
    llm_description: The unique identifier for the AI agent whose memories are being added.
    form: llm

  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
      ja_JP: 実行ID
    human_description:
      en_US: Unique identifier for the conversation session
      zh_Hans: 对话会话的唯一标识符
      pt_BR: Identificador único da sessão de conversa
      ja_JP: 会話セッションの一意識別子
    llm_description: The unique identifier for the conversation session.
    form: llm

  - name: metadata
    type: string
    required: false
    label:
      en_US: Metadata
      zh_Hans: 元数据
      pt_BR: Metadados
      ja_JP: メタデータ
    human_description:
      en_US: 'Additional metadata as JSON string (e.g., {"source": "chat", "priority": "high"})'
      zh_Hans: '作为JSON字符串的附加元数据（例如：{"source": "chat", "priority": "high"}）'
      pt_BR: 'Metadados adicionais como string JSON (ex: {"source": "chat", "priority": "high"})'
      ja_JP: 'JSON文字列としての追加メタデータ（例：{"source": "chat", "priority": "high"}）'
    llm_description: Additional metadata to associate with the memories as a JSON string.
    form: llm

  - name: custom_instructions
    type: string
    required: false
    label:
      en_US: Custom Instructions
      zh_Hans: 自定义指令
      pt_BR: Instruções Personalizadas
      ja_JP: カスタム指示
    human_description:
      en_US: Custom instructions for multimodal memory processing and extraction
      zh_Hans: 用于多模态记忆处理和提取的自定义指令
      pt_BR: Instruções personalizadas para processamento e extração de memória multimodal
      ja_JP: マルチモーダルメモリ処理と抽出のためのカスタム指示
    llm_description: Custom instructions for multimodal memory processing and extraction
    form: llm

  - name: custom_categories
    type: string
    required: false
    label:
      en_US: Custom Categories (JSON)
      zh_Hans: 自定义分类 (JSON)
      pt_BR: Categorias Personalizadas (JSON)
      ja_JP: カスタムカテゴリ (JSON)
    human_description:
      en_US: Custom categories for multimodal memory classification in JSON format
      zh_Hans: JSON格式的自定义分类用于多模态记忆分类
      pt_BR: Categorias personalizadas para classificação de memória multimodal em formato JSON
      ja_JP: JSON形式のマルチモーダルメモリ分類用カスタムカテゴリ
    llm_description: Custom categories for multimodal memory classification in JSON format
    form: llm

  - name: memory_priority
    type: string
    required: false
    label:
      en_US: Memory Priority
      zh_Hans: 记忆优先级
      pt_BR: Prioridade da Memória
      ja_JP: メモリ優先度
    human_description:
      en_US: Priority level for selective multimodal memory management (high, medium, low)
      zh_Hans: 选择性多模态记忆管理的优先级 (high, medium, low)
      pt_BR: Nível de prioridade para gerenciamento seletivo de memória multimodal (high, medium, low)
      ja_JP: 選択的マルチモーダルメモリ管理の優先レベル (high, medium, low)
    llm_description: Priority level for selective multimodal memory management
    form: llm

  - name: auto_prune
    type: boolean
    required: false
    default: false
    label:
      en_US: Auto Prune
      zh_Hans: 自动清理
      pt_BR: Limpeza Automática
      ja_JP: 自動プルーニング
    human_description:
      en_US: Enable automatic pruning of low-priority multimodal memories
      zh_Hans: 启用低优先级多模态记忆的自动清理
      pt_BR: Habilitar limpeza automática de memórias multimodais de baixa prioridade
      ja_JP: 低優先度マルチモーダルメモリの自動プルーニングを有効にする
    llm_description: Enable automatic pruning of low-priority multimodal memories
    form: form

  - name: use_async_client
    type: boolean
    required: false
    default: false
    label:
      en_US: Use Async Client
      zh_Hans: 使用异步客户端
      pt_BR: Usar Cliente Assíncrono
      ja_JP: 非同期クライアントを使用
    human_description:
      en_US: Enable asynchronous processing for better multimodal content performance
      zh_Hans: 启用异步处理以获得更好的多模态内容性能
      pt_BR: Habilitar processamento assíncrono para melhor desempenho de conteúdo multimodal
      ja_JP: より良いマルチモーダルコンテンツパフォーマンスのために非同期処理を有効にする
    llm_description: Enable asynchronous processing for better multimodal content performance
    form: form

extra:
  python:
    source: tools/add_multimodal_memory.py
